using System.Collections.Concurrent;
using System.Net;
using System.Net.Sockets;
using DnsServer.Protocol;
using DnsServer.Protocol.Building;
using DnsServer.Protocol.Parsing;

public class DnsForwarder : IDisposable
{
    private readonly IPEndPoint _resolverEndPoint;
    private readonly Socket _forwardingSocket;
    private readonly ConcurrentDictionary<ushort, TaskCompletionSource<DnsMessage>> _pendingQueries;
    private static int _nextPacketId = new Random().Next(1, ushort.MaxValue);

    public DnsForwarder(IPEndPoint resolverEndPoint)
    {
        _resolverEndPoint = resolverEndPoint;
        _pendingQueries = new ConcurrentDictionary<ushort, TaskCompletionSource<DnsMessage>>();
        
        _forwardingSocket = new Socket(AddressFamily.InterNetwork, SocketType.Dgram, ProtocolType.Udp);
        _forwardingSocket.Bind(new IPEndPoint(IPAddress.Any, 0)); // Bind to a random available port
    }

    public void StartReceiverLoop(CancellationToken token)
    {
        Task.Run(async () =>
        {
            var buffer = new byte[512];
            while (!token.IsCancellationRequested)
            {
                try
                {
                    var result = await _forwardingSocket.ReceiveFromAsync(buffer, SocketFlags.None, _resolverEndPoint, token);
                    var responseBytes = buffer.AsMemory(0, result.ReceivedBytes);
                    
                    var parser = new DnsPacketParser(responseBytes);
                    var responseMessage = parser.Parse();

                    if (_pendingQueries.TryRemove(responseMessage.Header.PacketId, out var tcs))
                    {
                        tcs.TrySetResult(responseMessage);
                    }
                }
                catch (OperationCanceledException)
                {
                    // Expected on shutdown
                    break;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error in forwarder receive loop: {ex.Message}");
                }
            }
        }, token);
    }

    public async Task<DnsMessage> ForwardQueryAsync(DnsMessage requestMessage, CancellationToken token)
    {
        var responseMessage = new DnsMessage();
        var allAnswers = new List<DnsAnswer>();

        // As per the challenge, we must split multi-question queries.
        var forwardingTasks = new List<Task<DnsMessage>>();
        foreach (var question in requestMessage.Questions)
        {
            forwardingTasks.Add(ForwardSingleQuestionAsync(question, token));
        }

        // Wait for all forwarded queries to complete
        await Task.WhenAll(forwardingTasks);

        // Merge answers from all responses
        foreach (var task in forwardingTasks)
        {
            if (task.IsCompletedSuccessfully)
            {
                allAnswers.AddRange(task.Result.Answers);
            }
        }
        
        // Construct the final response to the client
        responseMessage.Header = requestMessage.Header; // Start with the original header
        responseMessage.Header.IsResponse = true;
        responseMessage.Header.RecursionAvailable = true; // We are acting as a recursive resolver
        responseMessage.Header.AnswerRecordCount = (ushort)allAnswers.Count;
        responseMessage.Questions = requestMessage.Questions;
        responseMessage.Answers = allAnswers;

        return responseMessage;
    }

    private async Task<DnsMessage> ForwardSingleQuestionAsync(DnsQuestion question, CancellationToken token)
    {
        var newPacketId = (ushort)(Interlocked.Increment(ref _nextPacketId) & 0xFFFF);
        
        var queryMessage = new DnsMessage();
        queryMessage.Header.PacketId = newPacketId;
        queryMessage.Header.RecursionDesired = true;
        queryMessage.Header.QuestionCount = 1;
        queryMessage.Questions.Add(question);

        var builder = new DnsPacketBuilder();
        var queryBytes = builder.Build(queryMessage);

        var tcs = new TaskCompletionSource<DnsMessage>();
        _pendingQueries[newPacketId] = tcs;

        await _forwardingSocket.SendToAsync(queryBytes, SocketFlags.None, _resolverEndPoint, token);

        // Wait for the response with a timeout
        return await tcs.Task.WaitAsync(TimeSpan.FromSeconds(2), token);
    }

    public void Dispose()
    {
        _forwardingSocket.Dispose();
    }
}
