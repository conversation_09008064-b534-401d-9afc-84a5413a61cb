using System.Buffers.Binary;
using System.Text;
using DnsServer.Protocol;

namespace DnsServer.Protocol.Parsing;

public class DnsPacketParser
{
    private const int MaxPointerJumps = 10;
    private readonly ReadOnlyMemory<byte> _buffer;
    private int _offset;

    public DnsPacketParser(ReadOnlyMemory<byte> buffer)
    {
        _buffer = buffer;
        _offset = 0;
    }

    public DnsMessage Parse()
    {
        var message = new DnsMessage();
        ParseHeader(message.Header);
        ParseQuestions(message);
        // We don't parse answers from the request in this challenge
        return message;
    }

    private void ParseQuestions(DnsMessage message)
    {
        for (var i = 0; i < message.Header.QuestionCount; i++)
        {
            message.Questions.Add(ParseQuestion());
        }
    }

    private DnsQuestion ParseQuestion()
    {
        var name = DecodeDomainName();
        var recordType = BinaryPrimitives.ReadUInt16BigEndian(_buffer.Span.Slice(_offset));
        _offset += 2;
        var recordClass = BinaryPrimitives.ReadUInt16BigEndian(_buffer.Span.Slice(_offset));
        _offset += 2;
        return new DnsQuestion(name, recordType, recordClass);
    }

    private string DecodeDomainName()
    {
        var labels = new List<string>();
        var span = _buffer.Span;
        var currentOffset = _offset;
        var finalOffsetAfterName = -1;
        var jumps = 0;

        while (true)
        {
            if (currentOffset >= span.Length)
            {
                throw new ArgumentException("Invalid DNS packet: domain name decoding went out of bounds.");
            }

            var length = span[currentOffset];

            // Check for compression pointer
            if ((length & 0b1100_0000) == 0b1100_0000)
            {
                if (jumps++ > MaxPointerJumps)
                {
                    throw new ArgumentException("Invalid DNS packet: pointer loop detected.");
                }

                // If this is the first time we've jumped, mark the end of the name in the current context.
                if (finalOffsetAfterName == -1)
                {
                    finalOffsetAfterName = currentOffset + 2;
                }

                // Calculate the pointer offset
                var pointer = BinaryPrimitives.ReadUInt16BigEndian(span.Slice(currentOffset)) & 0x3FFF;
                currentOffset = pointer;
                continue;
            }

            // Check for end of name
            if (length == 0)
            {
                if (finalOffsetAfterName == -1)
                {
                    finalOffsetAfterName = currentOffset + 1;
                }
                break;
            }

            currentOffset++; // Move past length byte
            var label = Encoding.ASCII.GetString(span.Slice(currentOffset, length));
            labels.Add(label);
            currentOffset += length;
        }

        _offset = finalOffsetAfterName;
        return string.Join('.', labels);
    }

    private void ParseHeader(DnsHeader header)
    {
        if (_buffer.Length < 12) throw new ArgumentException("Invalid DNS packet: buffer is too small for a header.");
        var span = _buffer.Span;
        header.PacketId = BinaryPrimitives.ReadUInt16BigEndian(span.Slice(_offset));
        _offset += 2;
        byte flags1 = span[_offset++];
        byte flags2 = span[_offset++];
        header.IsResponse = (flags1 & (1 << 7)) != 0;
        header.OpCode = (byte)((flags1 >> 3) & 0b_0000_1111);
        header.AuthoritativeAnswer = (flags1 & (1 << 2)) != 0;
        header.Truncation = (flags1 & (1 << 1)) != 0;
        header.RecursionDesired = (flags1 & 1) != 0;
        header.RecursionAvailable = (flags2 & (1 << 7)) != 0;
        header.ResponseCode = (byte)(flags2 & 0b_0000_1111);
        header.QuestionCount = BinaryPrimitives.ReadUInt16BigEndian(span.Slice(_offset));
        _offset += 2;
        header.AnswerRecordCount = BinaryPrimitives.ReadUInt16BigEndian(span.Slice(_offset));
        _offset += 2;
        header.AuthorityRecordCount = BinaryPrimitives.ReadUInt16BigEndian(span.Slice(_offset));
        _offset += 2;
        header.AdditionalRecordCount = BinaryPrimitives.ReadUInt16BigEndian(span.Slice(_offset));
        _offset += 2;
    }
}
