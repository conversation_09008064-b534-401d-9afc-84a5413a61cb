using System.Buffers.Binary;
using DnsServer.Protocol;

namespace DnsServer.Protocol.Parsing;

public class DnsPacketParser
{
    private readonly ReadOnlyMemory<byte> _buffer;
    private int _offset;

    public DnsPacketParser(ReadOnlyMemory<byte> buffer)
    {
        _buffer = buffer;
        _offset = 0;
    }

    public DnsMessage Parse()
    {
        var message = new DnsMessage();
        ParseHeader(message.Header);
        // In future stages, we will parse questions and answers here.
        return message;
    }

    private void ParseHeader(DnsHeader header)
    {
        // A DNS packet must be at least 12 bytes long to contain a header.
        if (_buffer.Length < 12)
        {
            throw new ArgumentException("Invalid DNS packet: buffer is too small for a header.");
        }

        var span = _buffer.Span;

        // Read Packet ID (16 bits)
        header.PacketId = BinaryPrimitives.ReadUInt16BigEndian(span.Slice(_offset));
        _offset += 2;

        // Read Flags (16 bits)
        byte flags1 = span[_offset++];
        byte flags2 = span[_offset++];

        header.IsResponse = (flags1 & (1 << 7)) != 0;
        header.OpCode = (byte)((flags1 >> 3) & 0b_0000_1111);
        header.AuthoritativeAnswer = (flags1 & (1 << 2)) != 0;
        header.Truncation = (flags1 & (1 << 1)) != 0;
        header.RecursionDesired = (flags1 & 1) != 0;

        header.RecursionAvailable = (flags2 & (1 << 7)) != 0;
        // Skip Z reserved bits
        header.ResponseCode = (byte)(flags2 & 0b_0000_1111);

        // Read Counts
        header.QuestionCount = BinaryPrimitives.ReadUInt16BigEndian(span.Slice(_offset));
        _offset += 2;
        header.AnswerRecordCount = BinaryPrimitives.ReadUInt16BigEndian(span.Slice(_offset));
        _offset += 2;
        header.AuthorityRecordCount = BinaryPrimitives.ReadUInt16BigEndian(span.Slice(_offset));
        _offset += 2;
        header.AdditionalRecordCount = BinaryPrimitives.ReadUInt16BigEndian(span.Slice(_offset));
        _offset += 2;
    }
}
