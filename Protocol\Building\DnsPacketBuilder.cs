using System.Buffers.Binary;
using System.Text;
using DnsServer.Protocol;

namespace DnsServer.Protocol.Building;

public class DnsPacketBuilder
{
    private readonly byte[] _buffer;
    private int _offset;

    public DnsPacketBuilder(int size = 512)
    {
        _buffer = new byte[size];
        _offset = 0;
    }

    public ReadOnlyMemory<byte> Build(DnsMessage message)
    {
        _offset = 12; // Reserve space for the header

        foreach (var question in message.Questions)
        {
            BuildQuestion(question);
        }

        foreach (var answer in message.Answers)
        {
            BuildAnswer(answer);
        }

        var finalSize = _offset;
        _offset = 0;
        BuildHeader(message.Header);

        return _buffer.AsMemory(0, finalSize);
    }

    private void BuildAnswer(DnsAnswer answer)
    {
        EncodeDomainName(answer.Name);
        BinaryPrimitives.WriteUInt16BigEndian(_buffer.AsSpan(_offset), answer.RecordType);
        _offset += 2;
        BinaryPrimitives.WriteUInt16BigEndian(_buffer.AsSpan(_offset), answer.RecordClass);
        _offset += 2;
        BinaryPrimitives.WriteUInt32BigEndian(_buffer.AsSpan(_offset), answer.Ttl);
        _offset += 4;
        BinaryPrimitives.WriteUInt16BigEndian(_buffer.AsSpan(_offset), answer.RdLength);
        _offset += 2;
        Buffer.BlockCopy(answer.RData, 0, _buffer, _offset, answer.RData.Length);
        _offset += answer.RData.Length;
    }

    private void EncodeDomainName(string name)
    {
        if (string.IsNullOrEmpty(name) || name == ".")
        {
            // Root domain is represented by a single null byte
            _buffer[_offset++] = 0;
            return;
        }

        var labels = name.Split('.');
        foreach (var label in labels)
        {
            var labelBytes = Encoding.ASCII.GetBytes(label);
            _buffer[_offset++] = (byte)labelBytes.Length;
            Buffer.BlockCopy(labelBytes, 0, _buffer, _offset, labelBytes.Length);
            _offset += labelBytes.Length;
        }

        // Terminate the domain name with a null byte
        _buffer[_offset++] = 0;
    }

    private void BuildQuestion(DnsQuestion question)
    {
        EncodeDomainName(question.Name);
        BinaryPrimitives.WriteUInt16BigEndian(_buffer.AsSpan(_offset), question.RecordType);
        _offset += 2;
        BinaryPrimitives.WriteUInt16BigEndian(_buffer.AsSpan(_offset), question.RecordClass);
        _offset += 2;
    }

    private void BuildHeader(DnsHeader header)
    {
        // Ensure we have enough space for the header
        if (_buffer.Length < 12)
        {
            throw new ArgumentException("Buffer is too small for DNS header.");
        }

        // Section 4.1.1 of RFC 1035
        // Write Packet ID (16 bits)
        BinaryPrimitives.WriteUInt16BigEndian(_buffer.AsSpan(_offset), header.PacketId);
        _offset += 2;

        // Write Flags (16 bits total)
        // First byte of flags
        byte flags1 = 0;
        if (header.IsResponse) flags1 |= 1 << 7; // QR
        flags1 |= (byte)(header.OpCode << 3);    // OPCODE
        if (header.AuthoritativeAnswer) flags1 |= 1 << 2; // AA
        if (header.Truncation) flags1 |= 1 << 1;          // TC
        if (header.RecursionDesired) flags1 |= 1;         // RD
        _buffer[_offset++] = flags1;

        // Second byte of flags
        byte flags2 = 0;
        if (header.RecursionAvailable) flags2 |= 1 << 7; // RA
        // Z is reserved (3 bits), should be 0
        flags2 |= (byte)header.ResponseCode;             // RCODE
        _buffer[_offset++] = flags2;

        // Write QDCOUNT (16 bits)
        BinaryPrimitives.WriteUInt16BigEndian(_buffer.AsSpan(_offset), header.QuestionCount);
        _offset += 2;

        // Write ANCOUNT (16 bits)
        BinaryPrimitives.WriteUInt16BigEndian(_buffer.AsSpan(_offset), header.AnswerRecordCount);
        _offset += 2;

        // Write NSCOUNT (16 bits)
        BinaryPrimitives.WriteUInt16BigEndian(_buffer.AsSpan(_offset), header.AuthorityRecordCount);
        _offset += 2;

        // Write ARCOUNT (16 bits)
        BinaryPrimitives.WriteUInt16BigEndian(_buffer.AsSpan(_offset), header.AdditionalRecordCount);
        _offset += 2;
    }
}
