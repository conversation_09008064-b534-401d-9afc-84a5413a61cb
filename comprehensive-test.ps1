# Comprehensive DNS Server Test Suite
param(
    [string]$TestMode = "all"
)

$ErrorActionPreference = "Stop"

# Test configuration
$dnsServer = "127.0.0.1"
$dnsPort = 2053
$testResults = @()

function Write-TestResult {
    param($TestName, $Result, $Details = "")
    $status = if ($Result) { "PASS" } else { "FAIL" }
    $color = if ($Result) { "Green" } else { "Red" }
    Write-Host "[$status] $TestName" -ForegroundColor $color
    if ($Details) { Write-Host "    $Details" -ForegroundColor Gray }
    $script:testResults += @{ Name = $TestName; Result = $Result; Details = $Details }
}

function Send-DnsQuery {
    param(
        [byte[]]$QueryBytes,
        [int]$TimeoutMs = 5000
    )
    
    $udpClient = New-Object System.Net.Sockets.UdpClient
    $endpoint = New-Object System.Net.IPEndPoint([System.Net.IPAddress]::Parse($dnsServer), $dnsPort)
    
    try {
        $udpClient.Client.ReceiveTimeout = $TimeoutMs
        $udpClient.Send($QueryBytes, $QueryBytes.Length, $endpoint)
        $result = $udpClient.Receive([ref]$endpoint)
        return $result
    } finally {
        $udpClient.Close()
    }
}

function Create-DnsQuery {
    param(
        [string[]]$Domains,
        [ushort]$TransactionId = 0x1234,
        [ushort]$RecordType = 1  # A record
    )
    
    $query = @(
        ($TransactionId -shr 8), ($TransactionId -band 0xFF),  # Transaction ID
        0x01, 0x00,  # Flags (standard query, recursion desired)
        ($Domains.Count -shr 8), ($Domains.Count -band 0xFF),  # Questions count
        0x00, 0x00,  # Answer RRs: 0
        0x00, 0x00,  # Authority RRs: 0
        0x00, 0x00   # Additional RRs: 0
    )
    
    foreach ($domain in $Domains) {
        $labels = $domain.Split('.')
        foreach ($label in $labels) {
            $labelBytes = [System.Text.Encoding]::ASCII.GetBytes($label)
            $query += $labelBytes.Length
            $query += $labelBytes
        }
        $query += 0x00  # End of name
        $query += ($RecordType -shr 8), ($RecordType -band 0xFF)  # Type
        $query += 0x00, 0x01  # Class: IN
    }
    
    return [byte[]]$query
}

function Parse-DnsResponse {
    param([byte[]]$Response)
    
    if ($Response.Length -lt 12) {
        throw "Response too short"
    }
    
    return @{
        TransactionId = ($Response[0] -shl 8) + $Response[1]
        IsResponse = ($Response[2] -band 0x80) -ne 0
        Questions = ($Response[4] -shl 8) + $Response[5]
        Answers = ($Response[6] -shl 8) + $Response[7]
        ResponseCode = $Response[3] -band 0x0F
    }
}

# Test 1: Command-line argument parsing
Write-Host "`n=== Testing Command-Line Argument Parsing ===" -ForegroundColor Yellow

# Test invalid resolver format
Write-Host "Testing invalid resolver format..."
$process = Start-Process -FilePath "dotnet" -ArgumentList "run", "--no-build", "--", "--resolver", "invalid-format" -NoNewWindow -PassThru -RedirectStandardOutput -RedirectStandardError
$process.WaitForExit(5000)
$exitCode = $process.ExitCode
Write-TestResult "Invalid resolver format handling" ($exitCode -ne 0) "Exit code: $exitCode"

# Test valid resolver format
Write-Host "Testing valid resolver format..."
$process = Start-Process -FilePath "dotnet" -ArgumentList "run", "--no-build", "--", "--resolver", "*******:53" -NoNewWindow -PassThru -RedirectStandardOutput
Start-Sleep -Seconds 2
$isRunning = !$process.HasExited
if ($isRunning) { $process.Kill() }
Write-TestResult "Valid resolver format acceptance" $isRunning "Process started successfully"

Write-Host "`n=== Summary ===" -ForegroundColor Yellow
$passCount = ($testResults | Where-Object { $_.Result }).Count
$totalCount = $testResults.Count
Write-Host "Tests passed: $passCount/$totalCount" -ForegroundColor $(if ($passCount -eq $totalCount) { "Green" } else { "Red" })

if ($passCount -ne $totalCount) {
    Write-Host "`nFailed tests:" -ForegroundColor Red
    $testResults | Where-Object { !$_.Result } | ForEach-Object {
        Write-Host "  - $($_.Name): $($_.Details)" -ForegroundColor Red
    }
}
