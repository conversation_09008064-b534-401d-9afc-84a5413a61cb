# Test non-forwarding mode functionality
$dnsServer = "127.0.0.1"
$dnsPort = 2053

function Send-DnsQuery {
    param([byte[]]$QueryBytes)
    
    $udpClient = New-Object System.Net.Sockets.UdpClient
    $endpoint = New-Object System.Net.IPEndPoint([System.Net.IPAddress]::Parse($dnsServer), $dnsPort)
    
    try {
        $udpClient.Client.ReceiveTimeout = 5000
        $udpClient.Send($QueryBytes, $QueryBytes.Length, $endpoint)
        $result = $udpClient.Receive([ref]$endpoint)
        return $result
    } finally {
        $udpClient.Close()
    }
}

# Create DNS query for example.com
$query = @(
    0x12, 0x34,  # Transaction ID
    0x01, 0x00,  # Flags (standard query, recursion desired)
    0x00, 0x01,  # Questions: 1
    0x00, 0x00,  # Answer RRs: 0
    0x00, 0x00,  # Authority RRs: 0
    0x00, 0x00,  # Additional RRs: 0
    # Question: "example.com"
    0x07, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65,  # "example"
    0x03, 0x63, 0x6f, 0x6d,                          # "com"
    0x00,        # End of name
    0x00, 0x01,  # Type: A
    0x00, 0x01   # Class: IN
)

Write-Host "Testing non-forwarding mode..."
Write-Host "Query bytes: $([System.BitConverter]::ToString($query[0..11]))"
try {
    $response = Send-DnsQuery -QueryBytes $query

    Write-Host "Response received: $($response.Length) bytes"
    Write-Host "Full response: $([System.BitConverter]::ToString($response))"

    # Find the actual DNS header start (should start with our transaction ID 0x1234)
    $headerStart = -1
    for ($i = 0; $i -le $response.Length - 2; $i++) {
        if ($response[$i] -eq 0x12 -and $response[$i+1] -eq 0x34) {
            $headerStart = $i
            break
        }
    }

    if ($headerStart -eq -1) {
        throw "Could not find transaction ID in response"
    }

    Write-Host "DNS header starts at offset: $headerStart"
    Write-Host "Bytes at header: $($response[$headerStart].ToString('X2'))-$($response[$headerStart+1].ToString('X2'))-$($response[$headerStart+2].ToString('X2'))-$($response[$headerStart+3].ToString('X2'))"

    # Parse response (DNS uses big-endian format)
    Write-Host "Debug: response[$headerStart] = 0x$($response[$headerStart].ToString('X2')) = $($response[$headerStart])"
    Write-Host "Debug: response[$($headerStart+1)] = 0x$($response[$headerStart+1].ToString('X2')) = $($response[$headerStart+1])"
    Write-Host "Debug: ($($response[$headerStart]) -shl 8) = $(($response[$headerStart]) -shl 8)"
    $transactionId = ($response[$headerStart] -shl 8) + $response[$headerStart+1]
    $flags = ($response[$headerStart+2] -shl 8) + $response[$headerStart+3]
    $questions = ($response[$headerStart+4] -shl 8) + $response[$headerStart+5]
    $answers = ($response[$headerStart+6] -shl 8) + $response[$headerStart+7]
    $isResponse = ($flags -band 0x8000) -ne 0
    Write-Host "Transaction ID: 0x$($transactionId.ToString('X4')) (expected: 0x1234)"
    Write-Host "Is Response: $isResponse"
    Write-Host "Questions: $questions"
    Write-Host "Answers: $answers"
    
    # Check if response contains hardcoded ******* answer
    $expectedAnswer = @(0x08, 0x08, 0x08, 0x08)
    $hasExpectedAnswer = $false
    for ($i = 0; $i -le $response.Length - 4; $i++) {
        if ($response[$i] -eq 0x08 -and $response[$i+1] -eq 0x08 -and 
            $response[$i+2] -eq 0x08 -and $response[$i+3] -eq 0x08) {
            $hasExpectedAnswer = $true
            break
        }
    }
    
    Write-Host "Contains hardcoded ******* answer: $hasExpectedAnswer"
    
    # Verify expectations (transaction ID should match)
    $success = $transactionId -eq 0x1234 -and $isResponse -and $questions -eq 1 -and $answers -eq 1 -and $hasExpectedAnswer
    
    if ($success) {
        Write-Host "✅ Non-forwarding mode test PASSED" -ForegroundColor Green
    } else {
        Write-Host "❌ Non-forwarding mode test FAILED" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
}
