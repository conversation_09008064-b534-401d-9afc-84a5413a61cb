using System.Net;
using System.Net.Sockets;
using DnsServer.Protocol;
using DnsServer.Protocol.Building;
using DnsServer.Protocol.Parsing;

public class DnsServer
{
    private const int MaxUdpSize = 512; // As per DNS standards for non-EDNS
    private readonly IPEndPoint _listenEndPoint = new(IPAddress.Any, 2053);
    private readonly IPEndPoint? _resolverEndPoint;

    public DnsServer(IPEndPoint? resolverEndPoint)
    {
        _resolverEndPoint = resolverEndPoint;
    }

    public async Task StartAsync(CancellationToken token)
    {
        // Create and configure the main listening socket
        using var socket = new Socket(AddressFamily.InterNetwork, SocketType.Dgram, ProtocolType.Udp);
        socket.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReuseAddress, true);
        socket.Bind(_listenEndPoint);

        Console.WriteLine($"DNS Server listening on {_listenEndPoint}");

        // A single buffer is allocated once and reused for all receive operations
        var buffer = new byte[MaxUdpSize];
        
        // The remote endpoint for the received packet will be stored here
        var remoteEndPoint = new IPEndPoint(IPAddress.Any, 0);

        while (!token.IsCancellationRequested)
        {
            try
            {
                // Asynchronously wait for an incoming packet
                var result = await socket.ReceiveFromAsync(buffer, SocketFlags.None, remoteEndPoint, token);
                var receivedBytes = buffer.AsMemory(0, result.ReceivedBytes);

                // Offload processing to a separate task to keep the receive loop available
                _ = ProcessPacketAsync(socket, receivedBytes, (IPEndPoint)result.RemoteEndPoint, token);
            }
            catch (SocketException ex) when (ex.SocketErrorCode == SocketError.ConnectionReset)
            {
                // This is normal for UDP when a client closes - just continue listening
                continue;
            }
            catch (SocketException ex) when (ex.SocketErrorCode == SocketError.MessageSize)
            {
                // Packet was larger than our buffer - log and continue
                Console.WriteLine($"Received oversized packet (larger than {MaxUdpSize} bytes) - ignoring");
                continue;
            }
            catch (OperationCanceledException)
            {
                // Expected when cancellation token is triggered
                break;
            }
        }
    }

    private async Task ProcessPacketAsync(Socket socket, ReadOnlyMemory<byte> queryBytes, IPEndPoint remoteEp, CancellationToken token)
    {
        try
        {
            // 1. PARSE THE INCOMING REQUEST
            var parser = new DnsPacketParser(queryBytes);
            var requestMessage = parser.Parse();

            Console.WriteLine($"Received query {requestMessage.Header.PacketId} for OpCode {requestMessage.Header.OpCode} from {remoteEp}.");

            // 2. CONSTRUCT THE RESPONSE
            var responseMessage = new DnsMessage();
            var responseHeader = responseMessage.Header;

            // Set header fields based on the request
            responseHeader.PacketId = requestMessage.Header.PacketId; // Mimic the Packet ID
            responseHeader.IsResponse = true;
            responseHeader.OpCode = requestMessage.Header.OpCode; // Mimic the OpCode
            responseHeader.RecursionDesired = requestMessage.Header.RecursionDesired; // Mimic the RD flag

            // Set response-specific fields
            responseHeader.AuthoritativeAnswer = false;
            responseHeader.Truncation = false;
            responseHeader.RecursionAvailable = false; // We don't support recursion

            // Set Response Code based on OpCode
            if (requestMessage.Header.OpCode == 0) // Standard Query
            {
                responseHeader.ResponseCode = 0; // No Error
            }
            else
            {
                responseHeader.ResponseCode = 4; // Not Implemented
            }

            // For now, keep the question and answer hardcoded to pass previous stages' tests
            var question = new DnsQuestion("codecrafters.io", 1, 1);
            responseMessage.Questions.Add(question);
            responseHeader.QuestionCount = 1;

            // Only add an answer if the query was a standard one
            if (responseHeader.ResponseCode == 0)
            {
                var rdata = new byte[] { 8, 8, 8, 8 };
                var answer = new DnsAnswer("codecrafters.io", 1, 1, 60, rdata);
                responseMessage.Answers.Add(answer);
                responseHeader.AnswerRecordCount = 1;
            }

            // 3. BUILD AND SEND THE RESPONSE
            var builder = new DnsPacketBuilder();
            var responseBytes = builder.Build(responseMessage);

            await socket.SendToAsync(responseBytes, SocketFlags.None, remoteEp, token);
            Console.WriteLine($"Sent response for query {responseHeader.PacketId} to {remoteEp}.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error processing packet from {remoteEp}: {ex.Message}");
        }
    }
}
