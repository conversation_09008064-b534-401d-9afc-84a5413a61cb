using System.Net;
using System.Net.Sockets;
using DnsServer.Protocol;
using DnsServer.Protocol.Building;
using DnsServer.Protocol.Parsing;

namespace DnsServer;

public class DnsServer : IDisposable
{
    private const int MaxUdpSize = 512;
    private readonly IPEndPoint _listenEndPoint = new(IPAddress.Any, 2053);
    private readonly DnsForwarder? _forwarder;

    public DnsServer(IPEndPoint? resolverEndPoint)
    {
        if (resolverEndPoint != null)
        {
            _forwarder = new DnsForwarder(resolverEndPoint);
        }
    }

    public async Task StartAsync(CancellationToken token)
    {
        _forwarder?.StartReceiverLoop(token);

        using var socket = new Socket(AddressFamily.InterNetwork, SocketType.Dgram, ProtocolType.Udp);
        socket.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReuseAddress, true);
        socket.Bind(_listenEndPoint);

        Console.WriteLine($"DNS Server listening on {_listenEndPoint}");
        if (_forwarder != null)
        {
            Console.WriteLine("Server is in forwarding mode.");
        }

        var buffer = new byte[MaxUdpSize];
        var remoteEndPoint = new IPEndPoint(IPAddress.Any, 0);

        while (!token.IsCancellationRequested)
        {
            var result = await socket.ReceiveFromAsync(buffer, SocketFlags.None, remoteEndPoint, token);
            var receivedBytes = buffer.AsMemory(0, result.ReceivedBytes);

            _ = ProcessPacketAsync(socket, receivedBytes, (IPEndPoint)result.RemoteEndPoint, token);
        }
    }

    private async Task ProcessPacketAsync(Socket socket, ReadOnlyMemory<byte> queryBytes, IPEndPoint remoteEp, CancellationToken token)
    {
        try
        {
            var parser = new DnsPacketParser(queryBytes);
            var requestMessage = parser.Parse();

            DnsMessage responseMessage;

            if (_forwarder != null)
            {
                Console.WriteLine($"Forwarding query {requestMessage.Header.PacketId} for {requestMessage.Questions.FirstOrDefault()?.Name ?? "N/A"}");
                responseMessage = await _forwarder.ForwardQueryAsync(requestMessage, token);
            }
            else
            {
                // Logic from Stage 7 for non-forwarding mode
                Console.WriteLine($"Answering query {requestMessage.Header.PacketId} directly.");
                responseMessage = CreateDirectResponse(requestMessage);
            }

            var builder = new DnsPacketBuilder();
            var responseBytes = builder.Build(responseMessage);

            await socket.SendToAsync(responseBytes, SocketFlags.None, remoteEp, token);
            Console.WriteLine($"Sent response for query {responseMessage.Header.PacketId} to {remoteEp}.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error processing packet from {remoteEp}: {ex.GetType().Name} - {ex.Message}");
        }
    }

    private DnsMessage CreateDirectResponse(DnsMessage requestMessage)
    {
        var responseMessage = new DnsMessage();
        var header = responseMessage.Header;
        header.PacketId = requestMessage.Header.PacketId;
        header.IsResponse = true;
        header.OpCode = requestMessage.Header.OpCode;
        header.RecursionDesired = requestMessage.Header.RecursionDesired;
        header.RecursionAvailable = false;

        if (header.OpCode == 0)
        {
            header.ResponseCode = 0;
            foreach (var q in requestMessage.Questions)
            {
                responseMessage.Questions.Add(new DnsQuestion(q.Name, q.RecordType, q.RecordClass));
                responseMessage.Answers.Add(new DnsAnswer(q.Name, 1, 1, 60, new byte[] { 8, 8, 8, 8 }));
            }
        }
        else
        {
            header.ResponseCode = 4;
        }

        header.QuestionCount = (ushort)responseMessage.Questions.Count;
        header.AnswerRecordCount = (ushort)responseMessage.Answers.Count;
        return responseMessage;
    }

    public void Dispose()
    {
        _forwarder?.Dispose();
    }
}
