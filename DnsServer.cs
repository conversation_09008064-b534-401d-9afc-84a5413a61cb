using System.Net;
using System.Net.Sockets;
using DnsServer.Protocol;
using DnsServer.Protocol.Building;
using DnsServer.Protocol.Parsing;

namespace DnsServer;

public class DnsServer
{
    private const int MaxUdpSize = 512;
    private readonly IPEndPoint _listenEndPoint = new(IPAddress.Any, 2053);
    private readonly IPEndPoint? _resolverEndPoint;

    public DnsServer(IPEndPoint? resolverEndPoint)
    {
        _resolverEndPoint = resolverEndPoint;
    }

    public async Task StartAsync(CancellationToken token)
    {
        using var socket = new Socket(AddressFamily.InterNetwork, SocketType.Dgram, ProtocolType.Udp);
        socket.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReuseAddress, true);
        socket.Bind(_listenEndPoint);

        Console.WriteLine($"DNS Server listening on {_listenEndPoint}");

        var buffer = new byte[MaxUdpSize];
        var remoteEndPoint = new IPEndPoint(IPAddress.Any, 0);

        while (!token.IsCancellationRequested)
        {
            var result = await socket.ReceiveFromAsync(buffer, SocketFlags.None, remoteEndPoint, token);
            var receivedBytes = buffer.AsMemory(0, result.ReceivedBytes);

            _ = ProcessPacketAsync(socket, receivedBytes, (IPEndPoint)result.RemoteEndPoint, token);
        }
    }

    private async Task ProcessPacketAsync(Socket socket, ReadOnlyMemory<byte> queryBytes, IPEndPoint remoteEp, CancellationToken token)
    {
        try
        {
            // 1. PARSE THE INCOMING REQUEST
            var parser = new DnsPacketParser(queryBytes);
            var requestMessage = parser.Parse();

            Console.WriteLine($"Received query with {requestMessage.Header.QuestionCount} questions from {remoteEp}.");

            // 2. CONSTRUCT THE RESPONSE
            var responseMessage = new DnsMessage();
            var responseHeader = responseMessage.Header;

            // Copy header fields from the request
            responseHeader.PacketId = requestMessage.Header.PacketId;
            responseHeader.IsResponse = true;
            responseHeader.OpCode = requestMessage.Header.OpCode;
            responseHeader.RecursionDesired = requestMessage.Header.RecursionDesired;

            // Set response-specific fields
            responseHeader.RecursionAvailable = false;

            if (requestMessage.Header.OpCode == 0) // Standard Query
            {
                responseHeader.ResponseCode = 0; // No Error

                // Copy all questions from the request to the response
                foreach (var question in requestMessage.Questions)
                {
                    responseMessage.Questions.Add(new DnsQuestion(question.Name, question.RecordType, question.RecordClass));
                }

                // Create a hardcoded answer for each question
                foreach (var question in responseMessage.Questions)
                {
                    // The challenge specifies responding with an A record for each question.
                    var rdata = new byte[] { 8, 8, 8, 8 }; // A record for 8.8.8.8
                    responseMessage.Answers.Add(new DnsAnswer(question.Name, 1, 1, 60, rdata));
                }
            }
            else
            {
                responseHeader.ResponseCode = 4; // Not Implemented
            }

            // Set the counts in the header
            responseHeader.QuestionCount = (ushort)responseMessage.Questions.Count;
            responseHeader.AnswerRecordCount = (ushort)responseMessage.Answers.Count;

            // 3. BUILD AND SEND THE RESPONSE
            var builder = new DnsPacketBuilder();
            var responseBytes = builder.Build(responseMessage);

            await socket.SendToAsync(responseBytes, SocketFlags.None, remoteEp, token);
            Console.WriteLine($"Sent response with {responseMessage.Header.AnswerRecordCount} answers to {remoteEp}.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error processing packet from {remoteEp}: {ex.Message}");
        }
    }
}
