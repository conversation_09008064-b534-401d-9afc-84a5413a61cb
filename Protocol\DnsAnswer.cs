namespace DnsServer.Protocol;

public class DnsAnswer
{
    // The domain name that was queried.
    public string Name { get; set; }

    // Type of the resource record. (e.g., 1 for A record).
    public ushort RecordType { get; set; }

    // Class of the resource record. (e.g., 1 for IN).
    public ushort RecordClass { get; set; }

    // The number of seconds the result can be cached.
    public uint Ttl { get; set; }

    // The length of the RDATA field.
    public ushort RdLength { get; set; }

    // The data for the resource record.
    public byte[] RData { get; set; }

    public DnsAnswer(string name, ushort recordType, ushort recordClass, uint ttl, byte[] rData)
    {
        Name = name;
        RecordType = recordType;
        RecordClass = recordClass;
        Ttl = ttl;
        RData = rData;
        RdLength = (ushort)rData.Length;
    }
}
