namespace DnsServer.Protocol;

// Represents the 12-byte header of a DNS message.
public class DnsHeader
{
    // A 16-bit identifier assigned by the program that generates any kind of query.
    // This identifier is copied the corresponding reply and can be used by the requester
    // to match replies to outstanding queries.
    public ushort PacketId { get; set; }

    // A one bit field that specifies whether this message is a query (0), or a response (1).
    public bool IsResponse { get; set; }

    // A four bit field that specifies kind of query in this message.
    // This value is set by the originator of a query and copied into the response.
    // 0: a standard query (QUERY)
    // 1: an inverse query (IQUERY)
    // 2: a server status request (STATUS)
    // 3-15: reserved for future use
    public byte OpCode { get; set; }

    // Authoritative Answer - this bit is valid in responses, and specifies that the
    // responding name server is an authority for the domain name in question section.
    public bool AuthoritativeAnswer { get; set; }

    // TrunCation - specifies that this message was truncated due to length greater
    // than that permitted on the transmission channel.
    public bool Truncation { get; set; }

    // Recursion Desired - this bit may be set in a query and is copied into the response.
    // If RD is set, it directs the name server to pursue the query recursively.
    public bool RecursionDesired { get; set; }

    // Recursion Available - this be is set or cleared in a response, and denotes whether
    // recursive query support is available in the name server.
    public bool RecursionAvailable { get; set; }

    // Reserved for future use. Must be zero in all queries and responses.
    public byte Reserved { get; set; }

    // Response code - this 4 bit field is set as part of responses.
    // 0: No error condition
    // 1: Format error
    // 2: Server failure
    // 3: Name Error
    // 4: Not Implemented
    // 5: Refused
    // 6-15: Reserved for future use.
    public byte ResponseCode { get; set; }

    // A 16-bit integer specifying the number of entries in the question section.
    public ushort QuestionCount { get; set; }

    // A 16-bit integer specifying the number of resource records in the answer section.
    public ushort AnswerRecordCount { get; set; }

    // A 16-bit integer specifying the number of name server resource records in the authority records section.
    public ushort AuthorityRecordCount { get; set; }

    // A 16-bit integer specifying the number of resource records in the additional records section.
    public ushort AdditionalRecordCount { get; set; }
}
