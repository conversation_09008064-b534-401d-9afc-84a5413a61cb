namespace DnsServer.Protocol;

public class DnsQuestion
{
    // A domain name represented as a sequence of labels, where each label consists of a
    // length octet followed by that number of octets. The domain name terminates with the
    // zero length octet for the null label of the root.
    public string Name { get; set; }

    // A two octet code which specifies the type of the query.
    // e.g., 1 for A record, 5 for CNAME.
    public ushort RecordType { get; set; }

    // A two octet code that specifies the class of the query.
    // For example, IN (1) for Internet.
    public ushort RecordClass { get; set; }

    public DnsQuestion(string name, ushort recordType, ushort recordClass)
    {
        Name = name;
        RecordType = recordType;
        RecordClass = recordClass;
    }
}
