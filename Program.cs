using System.Net;

public class Program
{
    public static async Task Main(string[] args)
    {
        // Create a CancellationTokenSource to handle graceful shutdown
        using var cts = new CancellationTokenSource();

        // Register a handler for the Console.CancelKeyPress event (Ctrl+C)
        Console.CancelKeyPress += (_, e) =>
        {
            Console.WriteLine("Shutting down...");
            // Prevent the process from terminating immediately
            e.Cancel = true;
            // Trigger the cancellation token
            cts.Cancel();
        };

        // In later stages, we will parse arguments here to get the resolver endpoint
        IPEndPoint? resolverEndPoint = null; 

        var server = new DnsServer.DnsServer(resolverEndPoint);

        try
        {
            // Start the server and pass the cancellation token
            await server.StartAsync(cts.Token);
        }
        catch (OperationCanceledException)
        {
            // This is the expected exception when the token is cancelled.
            // We can exit gracefully.
            Console.WriteLine("Server stopped.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"An unexpected error occurred: {ex}");
        }
    }
}
